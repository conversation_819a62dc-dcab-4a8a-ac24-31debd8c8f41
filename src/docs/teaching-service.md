### In booking creation (service-booking)

- We need to create expected income log for teaching service
- Variables:
  - total duration (service-booking.duration \* service-booking.times)
  - service package qualification, subject, grade group etc.
    - to find this use service-booking.packUser -> service-pack-user_1
    - if service-pack-user.pid exists, use service-pack-user_1.pid -- findById -> service-pack-user_2.snapshot
    - else use service-pack-user_1.snapshot
    -
- create income-log with category = 'teaching_service', status = 0
- store the expected income amount in service-booking.servicerExpectedIncome({teachingService: 50, travel: 10})
- while creating session, session.servicerExpectedIncome = service-booking.servicerExpectedIncome

**NB**: For substitute, booking is not created. It is done on session patch with the substitute teacher info

### Actual Income on Cancel

- service-booking patchCancel
- Cancelled by booker: 20% of expected income
- Cancelled by servicer:
  - 2 hrs before start: 0
  - 2 hrs after start: -30% of expected income(penalty)
- Cancelled due to timeout:
  - -30% of expected income(penalty)

### Check how booking looks when school books

### Next

- check when .session is set in service-booking [done]
- accordingly set income-log session_status [done]
- verify for other mentoring types [partial_done_later]
- actual log creation on cancel, complete
- shift the logic for substitute
- handle for workshops
- check how to determine premium workshop
