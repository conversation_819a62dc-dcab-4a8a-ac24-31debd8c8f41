1. in @order.hooks.ts, instead of fetching balance from const account = await d.app.service(modelName).Model.findOne({\_id: buyer}).lean() we need to use wallet-balance(giftCard balance)

2. In gift-card-log.class.ts, in createGiftCardLog we need to use wallet-balance(giftCard balance) and update it accordingly instead of users/school-plan.

3. We can shift releaseReservedBalance from gift-card-log.service.ts to wallet-balance.service.ts, then update @order.class.ts to use wallet-balance.releaseReservedBalance instead of gift-card-log.releaseReservedBalance
