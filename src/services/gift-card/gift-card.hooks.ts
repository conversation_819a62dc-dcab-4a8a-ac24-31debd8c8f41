import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
import hook from '../../hook'

const {authenticate} = authentication.hooks

const mod: any = {
  async redeem(d: HookContext) {
    d.result = await d.service.redeem(d.params.query, d.params)
  },

  async balance(d: HookContext) {
    d.result = await d.service.getBalance(d.params.query, d.params)
  },
}

export default {
  before: {
    all: [authenticate('jwt')],
    find: [hook.disallowExternal],
    get: [
      (d: HookContext) => {
        if (d.id && mod[d.id]) return mod[d.id](d)
      },
    ],
    create: [hook.disallowExternal],
    update: [hook.disallowExternal],
    patch: [hook.disallowExternal],
    remove: [hook.disallowExternal],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
