import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {BadRequest, Forbidden, NotFound, GeneralError} from '@feathersjs/errors'
import * as crypto from 'crypto'
import {Types, LeanDocument} from 'mongoose'
import {stat} from 'fs'
import {commitTransactionSession, queueForCommit, rollbackTransactionSession, startTransactionSession} from '../../hooks/dbTransactions'
import {hasSchoolAccess} from '../../hooks/accessControl'

export class GiftCard extends Service {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  // Generate a unique, readable gift card code
  async generateUniqueCode(length: number = 16): Promise<string> {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    const bytes = crypto.randomBytes(length)
    let code = ''

    for (let i = 0; i < length; i++) {
      code += chars[bytes[i] % chars.length]
    }

    const existing = await this.Model.findOne({code})
    if (existing) {
      // If code exists, generate a new one recursively
      return this.generateUniqueCode()
    }

    return code
  }

  /**
   * todo
   * all school admins and subject coordinators should have access
   */
  async verifySchoolPermission(schoolId: string, user: any): Promise<void> {
    const hasAccess = await hasSchoolAccess(user._id, schoolId, ['admin', 'subjectCoordinator'], this.app)

    if (!hasAccess) {
      throw new Forbidden('Not authorized to perform operations for this school')
    }
  }

  // Create a gift card
  async create(data: any, params?: Params): Promise<any> {
    const {amount, isGift, recipientEmail, senderName, recipientName, giftMessage, image, order, isSchool, school} = data
    const user = params?.user
    const options = Acan.getTxnOptions(params)

    if (!user) {
      throw new Forbidden('Authentication required')
    }

    if (!amount || amount < 500 || !Number.isInteger(amount)) {
      throw new BadRequest('Amount must be an integer and at least 5')
    }

    if (!order) {
      throw new BadRequest('Order reference is required')
    }

    // const mongooseClient = this.app.get('mongooseClient')
    // const session = await mongooseClient.startSession()

    // session.startTransaction()

    const creatorType = isSchool ? 'school' : 'user'
    const creator = isSchool ? school : user._id
    let creatorAdmin = null

    if (isSchool && school) {
      // await this.verifySchoolPermission(school, user)
      creatorAdmin = user._id
    }

    const code = await this.generateUniqueCode()

    const giftCardData: any = {
      code,
      amount: amount,
      creator,
      creatorType,
      creatorAdmin,
      status: isGift ? 'active' : 'redeemed',
      isGift,
      order,
      image,
    }

    // Add gift-specific fields if it's a gift
    if (isGift) {
      if (!recipientEmail) {
        throw new BadRequest('Recipient email is required for gifts')
      }

      if (senderName) {
        giftCardData.senderName = senderName
      } else if (isSchool) {
        const schoolDoc = (await this.app.service('school-plan').Model.findOne({_id: school}, null, options).lean()) as any
        giftCardData.senderName = schoolDoc?.name || 'School'
      } else {
        giftCardData.senderName = user.name?.join(' ') || user.nickname
      }

      giftCardData.recipientEmail = recipientEmail
      giftCardData.recipientName = recipientName
      giftCardData.giftMessage = giftMessage
    } else {
      // Auto-redeem if not a gift
      giftCardData.redeemer = creator
      giftCardData.redeemerType = creatorType
      giftCardData.redeemerAdmin = creatorAdmin
      giftCardData.redeemedAt = new Date()
    }
    const giftCard = (await this.Model.create([giftCardData], options))[0]

    // const giftCard = await this.Model.create(giftCardData)

    // If not a gift, add to balance immediately
    if (!isGift) {
      // await this.addToBalance(giftCard, 'gift_card_purchase', session)
      await this.addToBalance(giftCard, 'gift_card_purchase', params)
    }

    // await session.commitTransaction()

    if (isGift) {
      await this.sendGiftEmail(giftCard, params)
    }

    return giftCard
  }

  // Redeem a gift card using its code
  async redeem(data: any, params?: Params): Promise<any> {
    const {code, school} = data
    const user = params?.user

    if (!user) {
      throw new Forbidden('Authentication required')
    }

    if (!code) {
      throw new BadRequest('Gift card code is required')
    }

    const session = await startTransactionSession(this.app)
    const transactionParams = {mongoose: {session}}
    let updatedGiftCard: any

    try {
      type GiftCardStatus = 'active' | 'redeemed' | 'canceled'

      interface GiftCard {
        _id: Types.ObjectId
        code: string
        status: GiftCardStatus
        amount: number
      }

      const giftCard: LeanDocument<GiftCard> | null = await this.Model.findOne({code}).lean()

      if (!giftCard) {
        return {status: 'failure', message: 'Invalid PIN. Gift card not found.'}
      }

      if (giftCard.status === 'redeemed') {
        return {status: 'failure', message: 'Gift card has already been redeemed.'}
      }
      const redeemerType = school ? 'school' : 'user'
      const redeemer = school ? school : user._id
      let redeemerAdmin = null

      if (school) {
        await this.verifySchoolPermission(school, user)
        redeemerAdmin = user._id
      }

      const now = new Date()

      updatedGiftCard = await this.Model.findOneAndUpdate(
        {
          code,
          status: 'active',
        },
        {
          $set: {
            status: 'redeemed',
            redeemer,
            redeemerType,
            redeemerAdmin,
            redeemedAt: now,
          },
        },
        {
          new: true,
          runValidators: true,
          session,
        }
      ).lean()

      if (!updatedGiftCard) {
        throw new NotFound('Gift card not found or already redeemed')
      }

      // Add to balance within the transaction
      await this.addToBalance(updatedGiftCard, 'gift_card_redeem', transactionParams)
      await commitTransactionSession(session, this.app, transactionParams)

      return updatedGiftCard
    } catch (error: any) {
      await rollbackTransactionSession(session)
      console.error('Gift card redemption failed:', error)
      throw new GeneralError('Failed to redeem gift card: ' + error?.message)
    }
  }

  // Cancel a gift card (for refunds)
  async cancel(giftCardId: any): Promise<any> {
    const updatedGiftCard = await this.Model.findByIdAndUpdate(giftCardId, {status: 'canceled'}, {new: true})
    return updatedGiftCard
  }

  // Add gift card amount to user/school balance
  async addToBalance(giftCard: any, category: string = 'gift_card_redeem', params: any): Promise<void> {
    const {_id, amount, redeemer, redeemerType} = giftCard

    try {
      await this.app.service('gift-card-log').createGiftCardLog(
        {
          uid: redeemer,
          tab: 'earn',
          source: 'redeem',
          category,
          value: amount,
          snapshot: giftCard,
          isSchool: redeemerType === 'school',
          businessId: _id,
        },
        params
      )
    } catch (error) {
      console.error('Failed to add gift card balance:', error)
      throw error
    }
  }

  // Get gift card balance for a user/school
  async getBalance(data: any, params?: Params): Promise<any> {
    const {school} = data
    const user = params?.user

    if (!user) {
      throw new Forbidden('Authentication required')
    }

    const accountId = school ? school : user._id
    const accountType = school ? 'school' : 'user'

    // Check school permissions if applicable
    if (school) {
      await this.verifySchoolPermission(school, user)
    }

    // Get balance directly from user/school document
    let account: any
    if (accountType === 'school') {
      account = await this.app.service('school-plan').Model.findOne({_id: accountId})
    } else {
      account = await this.app.service('users').Model.findOne({_id: accountId})
    }

    if (!account) {
      throw new NotFound('User or school not found')
    }

    return {
      balance: account.giftCardBalance || 0,
    }
  }

  // Send gift email notification
  async sendGiftEmail(giftCard: any, params: any): Promise<void> {
    const {recipientEmail, senderName, recipientName, giftMessage, code, amount, image} = giftCard

    if (!recipientEmail) {
      throw new BadRequest('Recipient email is required for gifts')
    }

    // Format amount for display (convert cents to dollars)
    const formattedAmount = (amount / 100).toFixed(2)

    // Send email using your notice-tpl service
    await queueForCommit(
      this.app,
      'notice-tpl',
      'send',
      [
        'SendGiftCard',
        {_id: '', email: recipientEmail as string},
        {
          recipientName: recipientName || 'Friend',
          senderName: senderName || 'Someone',
          displayUrl: `${SiteUrl}/v2/com/gift-cards/info...`,
          url: `${SiteUrl}/v2/com/gift-cards/info?code=${code}&amount=${formattedAmount}&image=${image}&giftMessage=${encodeURIComponent(
            giftMessage || ''
          )}&senderName=${encodeURIComponent(senderName || '')}`,
        },
      ],
      params
    )
  }

  // Validate gift card data for order creation
  async validateGiftCardOrder(data: any): Promise<{valid: boolean; message?: string}> {
    const {amount, isGift, recipientEmail} = data

    if (!amount || amount < 500 || !Number.isInteger(amount)) {
      return {
        valid: false,
        message: 'Gift card amount must be an integer and at least 5',
      }
    }

    if (isGift && !recipientEmail) {
      return {
        valid: false,
        message: 'Recipient email is required for gift cards sent as gifts',
      }
    }

    return {valid: true}
  }

  // Verify if a gift card can be canceled
  async verifyCancellationAllowed(giftCardId: string): Promise<{allowed: boolean; message?: string}> {
    // Find the gift card
    const giftCard = (await this.Model.findOne({_id: giftCardId})) as any
    if (!giftCard) {
      return {
        allowed: false,
        message: 'Gift card not found',
      }
    }

    if (giftCard.status === 'canceled') {
      return {
        allowed: false,
        message: 'Gift card is already canceled',
      }
    }

    if (!giftCard.isGift) {
      return {
        allowed: false,
        message: 'This gift card is not sent as a gift. You cannot cancel the order.',
      }
    }

    if (giftCard.status === 'redeemed') {
      return {
        allowed: false,
        message: 'The gift card has already been redeemed. You can no longer cancel the order.',
      }
    }

    // Check if the gift card was created within the last month (30 days)
    const createdAt = new Date(giftCard.createdAt).getTime()
    const oneMonthAgo = Date.now() - 30 * 24 * 60 * 60 * 1000

    if (createdAt < oneMonthAgo) {
      return {
        allowed: false,
        message: 'Gift cards can only be canceled within 30 days of purchase.',
      }
    }

    // All checks passed, cancellation is allowed
    return {
      allowed: true,
    }
  }
}
