import {Application} from '../../../declarations'

export class IncomeProcessor {
  constructor(private app: Application) {}

  async generateIncomeLog({order}: any) {
    /**
     * todo
     * order.price > 0
     * order.type IN ['unit', 'session_self_study', 'premium_cloud', 'prompt']
     * process each order.links
     * we will create income log of status: 1(actual) only for eligible links
     * so store the eligible links in an array
     * eligibility creteria: (link.style === 'premium_cloud' | 'unit' | 'prompt') or (order.type === 'sesession_self_study' && link.style === 'session')
     * keep a mapping of link.style to category, then set the category(setting_category will be exactly same as category)
     * unit: library_content, premium_cloud: premium_contents_task_sessions, prompt: new_prompt_content, session: self_study_content
     * after the loop is over if there are eligible links, then start db transaction
     * then create income log for each link using createIncomeLog from income-log.class.ts
     * then update order.isIncomeProcessed to true
     * commit transaction
     */
  }
}
