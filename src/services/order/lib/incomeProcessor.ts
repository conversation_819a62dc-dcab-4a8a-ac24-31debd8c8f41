import {Application} from '../../../declarations'
import {startTransactionSession, commitTransactionSession, rollbackTransactionSession, TxnParams} from '../../../hooks/dbTransactions'
import {shouldExecute} from '../../../hooks/cronScheduler'

export class IncomeProcessor {
  constructor(private app: Application) {}

  async generateIncomeLog({order}: any) {
    if (!order.price || order.price <= 0) {
      return
    }

    const eligibleOrderTypes = ['unit', 'session_self_study', 'premium_cloud', 'prompt', 'session_public']
    if (!eligibleOrderTypes.includes(order.type)) {
      return
    }

    const styleCategoryMapping: {[key: string]: string} = {
      unit: 'library_content',
      premium_cloud: 'premium_contents_task_sessions',
      prompt: 'new_prompt_content',
    }

    // Process each link and collect eligible ones
    const eligibleLinks = []
    for (const link of order.links || []) {
      // const remainingPrice = Number((link.price - link.refundPrice).toFixed(0))
      // if (remainingPrice <= 0) {
      //   continue
      // }

      if (!link.goods?.uid) continue

      let isEligible = false
      let category = null

      if (['premium_cloud', 'unit', 'prompt'].includes(link.style)) {
        isEligible = true
        category = styleCategoryMapping[link.style]
      } else if (order.type === 'session_self_study' && link.style === 'session') {
        isEligible = true
        category = 'self_study_content'
      } else if (order.type === 'session_public' && link.style === 'session') {
        isEligible = validWorkshopTypes.includes(link.goods.type)
        category = 'teaching_service'
      }

      if (isEligible && category) {
        eligibleLinks.push({
          link,
          category,
          setting_category: category,
        })
      }
    }

    if (eligibleLinks.length === 0) {
      await this.markOrderAsProcessed(order._id)
      return
    }
    console.log('eligibleLinks:', eligibleLinks.length)
    const session = await startTransactionSession(this.app)
    const transactionParams = {mongoose: {session}, sideEffectsToExecute: []}

    try {
      for (const eligibleLink of eligibleLinks) {
        switch (eligibleLink.category) {
          case 'teaching_session_own':
          case 'teaching_session_classcipe': {
            await this.insertWorkhopIncomeLog(eligibleLink, order, transactionParams)
            break
          }
          default: {
            await this.insertCommonIncomeLog(eligibleLink, order, transactionParams)
          }
        }
      }

      await this.app.service('order').Model.updateOne({_id: order._id}, {$set: {incomeStatus: 'actual_processed'}}, {session})

      await commitTransactionSession(session, this.app, transactionParams)
      console.log(`Order ${order._id}: Income processing completed successfully`)
    } catch (error) {
      await rollbackTransactionSession(session)
      throw error
    }
  }

  async insertCommonIncomeLog(eligibleLink: {link: any; category: string; setting_category: string}, order: any, params: TxnParams) {
    const {link, category, setting_category} = eligibleLink
    const incomeLogData = {
      uid: link.goods.uid,
      isSchool: false,
      tab: 'earn',
      category,
      setting_category,
      status: 1,
      businessId: `${order._id.toString()}-${link.id.toString()}`,
      event_details: {
        id: link.id,
        name: link.name,
        cover: link.cover,
        style: link.style,
        orderId: order._id.toString(),
        orderType: order.type,
      },
      amount: link.price || 0,
    }

    const incomePromises = [this.app.service('income-log').createIncomeLog(incomeLogData, params)]

    if (link.style === 'premium_cloud' && link.goods.approval?.approver) {
      const auditorIncomeLogData = {
        ...incomeLogData,
        uid: link.goods.approval.approver,
        category: 'premium_content_audit',
        setting_category: 'premium_content_audit',
      }
      incomePromises.push(this.app.service('income-log').createIncomeLog(auditorIncomeLogData, params))
    }

    await Promise.all(incomePromises)
  }

  async insertWorkhopIncomeLog(eligibleLink: {link: any; category: string; setting_category: string}, order: any, params: TxnParams) {
    const {link, category, setting_category} = eligibleLink
    // we need to insert log for link.goods with link.goods.uid as uid
    // if link.goods.type is either of unitCourses, studentCourses, pdCourses
    // then isParent: true
    // then if link.goods.type is either of unitCourses, studentCourses, pdCourses && link.goods.childs.length
    // fetch all child documents: app.service('session')._id: link.goods.childs[i]._id
    // for each of the child documents, insert log with child.uid as uid and isParent: false, parentId: inserted log id from above
    // take name, cover, etc. from respective session doc. link.goods is also a session document
  }

  async processIncomeCron() {
    // Check if should execute (every 30 minutes = 48 times per day)
    // if (!shouldExecute(48)) {
    //   return
    // }

    try {
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000)

      const ordersToProcess = await this.app
        .service('order')
        .Model.find({
          incomeStatus: 'actual_pending',
          // createdAt: {$lt: tenMinutesAgo},
        })
        .limit(100) // Process max 100 orders per run to avoid timeout
        .lean()

      console.log(`Income processing cron: Found ${ordersToProcess.length} orders to process`)

      for (const order of ordersToProcess) {
        try {
          await this.generateIncomeLog({order})
        } catch (error) {}
      }
    } catch (error) {}
  }

  private async markOrderAsProcessed(orderId: string) {
    try {
      await this.app.service('order').Model.updateOne({_id: orderId}, {$set: {incomeStatus: 'actual_processed'}})
    } catch (error) {}
  }
}

const validWorkshopTypes = ['taskWorkshop', 'unitCourses', 'studentWorkshop', 'studentCourses', 'workshop', 'pdCourses']

export const isIncomePending = (orderType: string, orderStatus: number, price: number) => {
  if (orderStatus !== 200 || !price || price <= 0) {
    return null
  }
  let incomeStatus: string | null = null
  if (['unit', 'session_self_study', 'premium_cloud', 'prompt'].includes(orderType)) {
    incomeStatus = 'actual_pending'
  } else if (['session_public'].includes(orderType)) {
    incomeStatus = 'expected_pending'
  }
  return incomeStatus
}
