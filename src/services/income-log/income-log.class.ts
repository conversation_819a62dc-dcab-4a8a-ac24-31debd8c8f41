import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {sortBySysCurriculum} from '../service-auth/unit/dict'
import {commitTransactionSession, rollbackTransactionSession, startTransactionSession, TxnParams} from '../../hooks/dbTransactions'

type UpdateLog = {value: number; notes: string; event_details: any}
export class IncomeLog extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async createIncomeLog(data: any, params: TxnParams): Promise<any> {
    const {uid, isSchool, tab, category, setting_category, status, isParent, businessId, event_details, notes, amount}: any = data

    if (!Acan.isValidNumber(amount)) return
    let value = Math.floor(Number(amount))

    if (setting_category) {
      const incomeSetting = await this.app.service('income-setting').calcIncome({category, amount})
      if (incomeSetting.income === 0) return
      value = incomeSetting.income
    }
    console.log('Income_value_0', status, value)
    if (status === 0 && value === 0) return
    console.log('Income_value_1', status, value)

    let updatedBalance: any = {}
    let actualAt: any
    if (status === 1) {
      updatedBalance = await this.app.service('wallet-balance').updateBalance(uid, isSchool, 'income', value, undefined, params)
      actualAt = new Date()
    } else {
      updatedBalance = await this.app.service('wallet-balance').getBalance({uid, balanceType: 'income'}, Acan.mergeTxnParams(params, {user: params.user}))
    }

    let expected = undefined
    if (status === 0) expected = value

    const options = Acan.getTxnOptions(params)
    return this.Model.findOneAndUpdate(
      {
        uid: uid,
        businessId: businessId,
        category: category,
      },
      {
        $set: {
          isSchool,
          tab,
          category,
          setting_category,
          value,
          total: updatedBalance.availableBalance,
          expected,
          actualAt,
          status,
          isParent,
          businessId,
          event_details,
          notes,
        },
      },
      {upsert: true, ...options}
    )
  }

  async updateIncomeLog(query: {uid: string; isSchool?: boolean; businessId: string; category: string; status: number}, data: UpdateLog, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const updatedBalance = await this.app.service('wallet-balance').updateBalance(query.uid, query.isSchool, 'income', data.value, undefined, params)
    await this.Model.updateOne(
      query,
      {
        $set: {
          ...data,
          total: updatedBalance.availableBalance,
          actualAt: new Date(),
          status: 1,
        },
      },
      options
    )
  }

  async genActualIncome({bookingId, bookingDoc, sessionId, sessionDoc}: any, params?: TxnParams) {
    // - Cancelled by booker: 20% of expected income
    // - Cancelled by servicer:
    // - 2 hrs before start: 0
    // - 2 hrs after start: -30% of expected income(penalty)
    // - timeout: -20% of expected income(penalty)
    let uid = ''
    let cancelledBy = ''
    let cancelledAt = ''
    let sessionStartAt = ''
    let businessId = ''
    if (bookingId || bookingDoc) {
      const booking: any = bookingDoc || (await this.app.service('service-booking').Model.findById(bookingId))
      if (!booking || booking?.type === 'serviceTask' || !booking.cancel || booking.incomeStatus !== 'actual_pending') return
      uid = booking.servicer
      cancelledBy = booking.cancel
      cancelledAt = booking.canceledAt
      sessionStartAt = booking.start
      businessId = bookingId
    } else if (sessionId || sessionDoc) {
      const session: any = sessionDoc || (await this.app.service('session').Model.findById(sessionId))
    } else return

    const expectedIncomeLog: any = await this.app.service('income-log').Model.findOne({
      uid,
      businessId,
      category: 'teaching_service',
    })
    if (!expectedIncomeLog) return

    let patchData: UpdateLog
    // new Date(doc.start).getTime() - Date.now() > 7200000
    const cancelledBefore2Hrs = new Date(sessionStartAt).getTime() - new Date(cancelledAt).getTime() > 2 * 60 * 60 * 1000
    if (cancelledBy === 'booker') {
      const value = Math.floor(expectedIncomeLog.value * 0.2)
      patchData = {
        value,
        notes: 'Amount change due to user refund',
        event_details: {
          ...(expectedIncomeLog.event_details || {}),
          session_status: 'cancelled',
          cancelledBy: 'booker',
          cancelledAt,
        },
      }
    } else if (cancelledBy === 'servicer' && cancelledBefore2Hrs) {
      patchData = {
        value: 0,
        notes: ' Amount change due to user refund (cancelled by facilitator)',
        event_details: {
          ...(expectedIncomeLog.event_details || {}),
          session_status: 'cancelled',
          cancelledBy: 'servicer',
          cancelledAt,
        },
      }
    } else if (cancelledBy === 'servicer' && !cancelledBefore2Hrs) {
      const value = -1 * Math.floor(expectedIncomeLog.value * 0.3)
      patchData = {
        value,
        notes: 'Amount change due to user refund (cancelled by facilitator)',
        event_details: {
          ...(expectedIncomeLog.event_details || {}),
          session_status: 'cancelled',
          cancelledBy: 'servicer',
          cancelledAt,
        },
      }
    } else if (cancelledBy === 'timeout') {
      const value = -1 * Math.floor(expectedIncomeLog.value * 0.2)
      patchData = {
        value,
        notes: 'Amount change due to penalty',
        event_details: {
          ...(expectedIncomeLog.event_details || {}),
          session_status: 'cancelled',
          cancelledBy: 'timeout',
          cancelledAt,
        },
      }
    } else return

    const session = await startTransactionSession(this.app)
    const transactionParams = {mongoose: {session}, sideEffectsToExecute: []}
    try {
      await this.app.service('income-log').updateIncomeLog(
        {
          uid,
          businessId,
          category: 'teaching_service',
          status: 0,
        },
        patchData,
        transactionParams
      )

      await this.app.service('service-booking').Model.updateOne(
        {_id: businessId},
        {
          $set: {
            incomeStatus: 'actual_processed',
            'serviceIncome.actual': patchData.value,
          },
        },
        {session}
      )
      await commitTransactionSession(session, this.app, transactionParams)
    } catch (error) {
      await rollbackTransactionSession(session)
    }
  }

  async calcTeachingServiceIncome({bookingId, bookingDoc, sessionId, sessionDoc}: any) {
    const result = {teachingService: 0, travel: 0}
    let packUserId: string = ''
    let duration = 0
    let bookingTimes = 0
    if (bookingId || bookingDoc) {
      const booking: any = bookingDoc || (await this.app.service('service-booking').Model.findById(bookingId))
      if (!booking || booking?.type === 'serviceTask' || !booking.packUser || booking.cancel) return result
      packUserId = booking.packUser
      duration = booking.duration * booking.times
      bookingTimes = booking.times
    } else if (sessionId || sessionDoc) {
      const session: any = sessionDoc || (await this.app.service('session').Model.findById(sessionId))
      if (!session || !session.packUser) return result
      packUserId = session.packUser
      duration = this.calcDurationMin({start: session.start, end: session.end})
    } else return result
    console.log('packUserId', packUserId)
    console.log('duration', duration)
    if (!Acan.isValidNumber(duration) || duration <= 0) return result

    let servicePackage: any = {}
    const packUser: any = await this.app.service('service-pack-user').Model.findById(packUserId)
    if (!packUser || !packUser.snapshot) return result

    servicePackage = packUser.snapshot

    const isContentOrientated = packUser.snapshot.type === 'content' && packUser.pid
    console.log('isContentOrientated', isContentOrientated)

    if (packUser.snapshot.type === 'content' && packUser.pid) {
      const parentPackUser: any = await this.app.service('service-pack-user').Model.findById(packUser.pid)
      if (!parentPackUser || !parentPackUser.snapshot) return result
      servicePackage = parentPackUser.snapshot
      console.log('parentPackUser', parentPackUser._id)
    }

    if (servicePackage.break && servicePackage.break > 0 && bookingTimes) {
      duration = duration - servicePackage.break * bookingTimes
    }
    console.log('duration_1', duration, servicePackage.break, bookingTimes)
    console.log('serviceRoles', servicePackage.serviceRoles)
    console.log('mentoringType', servicePackage.mentoringType)
    if (!['mentoring', 'substitute', 'consultant'].includes(servicePackage.serviceRoles)) return result

    const {config, sysSubjectData} = await this.getRateConfig(servicePackage.mentoringType)
    console.log('config', !!config, !!sysSubjectData)

    const commonConfig = {
      mentoringType: servicePackage.mentoringType,
      qualification: servicePackage.qualification,
      gradeGroup: servicePackage.gradeGroup,
      config,
      sysSubjectData,
    }
    console.log('qualification', commonConfig.qualification)
    console.log('curriculum', servicePackage.curriculum)
    console.log('gradeGroup', servicePackage.gradeGroup)
    console.log('sysSubjectData', JSON.stringify(sysSubjectData))
    const hourRateList: number[] = []
    // console.log('rate_config', JSON.stringify(config))
    if (servicePackage.mentoringType === 'academic') {
      const subjects: string[] = isContentOrientated ? [packUser.snapshot.subject] : servicePackage.subject || []

      subjects.forEach((subject: string) => {
        console.log('subject', subject)
        const rate = this.calcHourlyRate({
          ...commonConfig,
          curriculum: servicePackage.curriculum,
          subject: subject,
        })
        console.log('rate', rate)
        hourRateList.push(rate)
      })
    } else {
      const topics = packUser.snapshot?.topic || []

      topics.forEach((topic: any) => {
        console.log('topic', topic)
        if (typeof topic === 'string') return
        const rate = this.calcHourlyRate({
          ...commonConfig,
          topicId: topic._id?.toString(),
        })
        console.log('rate', rate)
        hourRateList.push(rate)
      })
    }
    console.log('hourRateList', hourRateList)
    if (hourRateList.length === 0) return result

    const teachingServiceAmount = Math.max(...hourRateList) * (duration / 60)
    console.log('teachingServiceAmount', teachingServiceAmount)
    console.log('isOnCampus', servicePackage.isOnCampus)
    const travelAmount = await this.campusPrice(servicePackage)
    console.log('travelAmount', travelAmount)

    return {
      teachingService: Number(teachingServiceAmount.toFixed(2)),
      travel: Number(travelAmount.toFixed(2)),
    }
  }

  async campusPrice(servicePackage: any) {
    if (!servicePackage.isOnCampus || !servicePackage.country || !servicePackage.city) return 0
    const campuses = (await this.app.service('campus-location').Model.find({country: servicePackage.country, city: servicePackage.city})) as {
      compensationHour: number
      experienceRate: any
      tutorRate: any
    }[]
    const currentCampus = campuses[0]
    if (!currentCampus) return 0
    let hourlyRate = 0
    const {gradeGroup = []} = servicePackage
    const totalHour = currentCampus?.compensationHour
    const currentCampusHourRate = servicePackage?.qualification === 'experiencedTeacher' ? currentCampus?.experienceRate : currentCampus?.tutorRate
    hourlyRate = currentCampusHourRate[gradeGroup?.[0]] || 0
    const totalPrice = (totalHour * hourlyRate).toFixed(2)

    return parseFloat(totalPrice)
  }

  async getRateConfig(mentoringType: string) {
    const serviceKey = `Service:${'mentoring'}:${[mentoringType]}`
    const config: any = await this.app.service('conf').Model.findById(serviceKey)
    if (config?.val?.curriculum?.length) {
      config.val.curriculum = sortBySysCurriculum(config.val.curriculum)
    }
    let sysSubjectData: any
    if (['essay', 'teacherTrainingSubject'].includes(mentoringType)) {
      sysSubjectData = await this.app.service('subjects').Model.findOne({uid: '1', subjectCode: mentoringType, curriculum: 'pd'})
    }
    return {config: config?.val, sysSubjectData}
  }

  calcHourlyRate({mentoringType, qualification, curriculum, subject, topicId, gradeGroup, config, sysSubjectData}: any): number {
    let result = 0
    switch (mentoringType) {
      case 'academic': {
        result = this.getCurriculumHourRate({
          qualification,
          curriculum,
          config,
          subject,
          gradeGroup,
        })
        break
      }

      case 'essay':
      case 'teacherTrainingSubject': {
        if (!sysSubjectData) return 0

        let topicFirstLayerId: any
        sysSubjectData?.['topic']?.some((firstLayer: any) => {
          console.log('firstLayer', firstLayer._id?.toString())
          firstLayer.child.some((secondLayer: any) => {
            console.log('secondLayer', secondLayer._id?.toString())
            if (secondLayer._id?.toString() === topicId) {
              topicFirstLayerId = firstLayer._id.toString()
              return true
            }
            return false
          })
          if (topicFirstLayerId) return true
          return false
        })
        console.log('topicFirstLayerId', topicFirstLayerId, topicId)
        result = this.getTopicHourRate({
          qualification,
          topicFirstLayerId,
          topicId,
          config,
          gradeGroup,
        })
        break
      }

      // todo, maybe keep these in default. Otherwise how to scale later?
      // case 'overseasStudy':
      // case 'teacherTraining':
      // case 'steam':
      // case 'academicPlanning':
      // case 'personalStatement':
      // case 'interest':
      default: {
        result = this.getCommonHourRate({
          qualification,
          config,
          topicId,
          gradeGroup,
        })
        break
      }
    }

    return result
  }

  calcDurationMin({start, end}: {start: string; end: string}) {
    return Math.floor((new Date(end).getTime() - new Date(start).getTime()) / 60000)
  }

  getCurriculumHourRate = ({qualification, curriculum, config, subject, gradeGroup}: any) => {
    if (!qualification) return 0
    const currentConfig = config?.hourRate?.filter((e: any) => e.qualification === qualification && e.curriculum === curriculum) || []
    const target = currentConfig.find((e: any) => e.default === false && e?.value?.includes(subject))

    const defaultTarget = currentConfig.find((e: any) => e.default === true)
    if (target?.gradeGroup?.some((e: string) => gradeGroup?.includes(e))) {
      return target?.price
    }
    return defaultTarget?.price || 0
  }

  getTopicHourRate = ({qualification, topicId, config, topicFirstLayerId, gradeGroup}: any) => {
    if (!qualification || !topicFirstLayerId) return 0
    const currentConfig = config?.hourRate?.filter((e: any) => e.qualification === qualification && e.topic === topicFirstLayerId) || []
    console.log('currentConfig', JSON.stringify(currentConfig))
    const target = currentConfig.find((e: any) => e.default === false && e?.value?.includes(topicId))
    console.log('target', target)
    const defaultTarget = currentConfig.find((e: any) => e.default === true)
    if (target?.gradeGroup?.some((e: string) => gradeGroup?.includes(e))) {
      console.log('target?.gradeGroup?', target?.price)
      return target?.price
    }
    console.log('defaultTarget?.price', defaultTarget?.price)
    return defaultTarget?.price || 0
  }

  getCommonHourRate = ({qualification, config, topicId, gradeGroup}: any) => {
    if (!qualification) return 0
    const currentConfig = config?.hourRate?.filter((e: any) => e.qualification === qualification) || []
    const target = currentConfig.find((e: any) => e.default === false && e?.value?.includes(topicId))
    const defaultTarget = currentConfig.find((e: any) => e.default === true)
    if (target?.gradeGroup?.some((e: string) => gradeGroup?.includes(e))) {
      return target?.price
    }
    return defaultTarget?.price || 0
  }
}
