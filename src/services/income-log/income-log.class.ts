import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

export class IncomeLog extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async createIncomeLog(data: any, params: Params): Promise<any> {
    const {uid, setting_category, category, expected, status, isSchool, isParent, businessId, event_details, session_status, notes, amount}: any = data

    const incomeSetting = await this.app.service('income-setting').calcIncome({category, amount})
    let value = incomeSetting.income

    if (!incomeSetting.success) {
      // calculate by other way
    }

    return this.Model.findOneAndUpdate(
      {
        uid: uid,
        businessId: businessId,
        category: category,
      },
      {
        $set: {
          setting_category,
          category,
          value,
          // total:
          expected,
          status,
          isSchool,
          isParent,
          businessId,
          event_details,
          session_status,
          notes,
        },
      },
      {upsert: true}
    )
  }
}
