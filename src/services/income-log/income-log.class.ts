import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

export class IncomeLog extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async createIncomeLog(data: any, params: Params): Promise<any> {
    const options = Acan.getTxnOptions(params)
    const {uid, isSchool, tab, category, setting_category, expected, status, isParent, businessId, event_details, session_status, notes, amount}: any = data

    const incomeSetting = await this.app.service('income-setting').calcIncome({category, amount})
    let value = incomeSetting.income

    if (value === 0) {
      return
    }

    if (!incomeSetting.success) {
      // calculate by other way
    }

    let updatedBalance: any = {}
    let actualAt: any
    if (status === 1) {
      updatedBalance = await this.app.service('wallet-balance').updateBalance(uid, isSchool, 'income', value, undefined, params)
      actualAt = new Date()
    } else {
      updatedBalance = await this.app.service('wallet-balance').getBalance({uid, balanceType: 'income'}, {user: params.user})
    }

    return this.Model.findOneAndUpdate(
      {
        uid: uid,
        businessId: businessId,
        category: category,
      },
      {
        $set: {
          isSchool,
          tab,
          category,
          setting_category,
          value,
          total: updatedBalance.availableBalance,
          expected,
          actualAt,
          status,
          isParent,
          businessId,
          event_details,
          session_status,
          notes,
        },
      },
      {upsert: true, ...options}
    )
  }
}
