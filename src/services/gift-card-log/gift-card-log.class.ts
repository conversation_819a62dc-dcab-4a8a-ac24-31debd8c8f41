import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {BadRequest} from '@feathersjs/errors'
import {TxnParams} from '../../hooks/dbTransactions'

interface CreateGiftCardLog {
  uid: string
  tab: string
  source: string
  category: string
  value: number
  businessId?: string
  isSchool: boolean
  snapshot?: any
  reserveBalance?: boolean
}
export class GiftCardLog extends Service {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async createGiftCardLog(data: CreateGiftCardLog, params?: TxnParams): Promise<any> {
    const {uid, tab, source, category, value, businessId, snapshot, isSchool, reserveBalance} = data

    if (!uid || !tab || !source || !category || value === undefined) {
      throw new BadRequest('Missing required fields')
    }

    const numericValue = Number(value)
    const options = Acan.getTxnOptions(params)

    // Update wallet balance
    let updatedBalance: any
    if (reserveBalance && numericValue < 0) {
      // Reserve the balance (move from available to reserved)
      updatedBalance = await this.app.service('wallet-balance').reserveBalance(uid, isSchool, 'giftCard', Math.abs(numericValue), params)
    } else if (tab === 'earn') {
      // Add to available balance
      updatedBalance = await this.app.service('wallet-balance').updateBalance(uid, isSchool, 'giftCard', numericValue, businessId, params)
    } else if (tab === 'claim') {
      // Deduct from available balance
      updatedBalance = await this.app.service('wallet-balance').updateBalance(uid, isSchool, 'giftCard', numericValue, businessId, params)
    } else {
      throw new BadRequest('Invalid tab value')
    }

    // Create the log entry
    await this.Model.create(
      [
        {
          uid,
          tab,
          source,
          category,
          value: numericValue,
          total: updatedBalance.availableBalance + updatedBalance.reservedBalance, // totalBalance
          businessId,
          snapshot,
          isSchool: isSchool || false,
        },
      ],
      options
    )

    return updatedBalance
  }
}
