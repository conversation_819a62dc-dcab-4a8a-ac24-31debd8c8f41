import {Application} from '../declarations'
import test from './test/test.service'
import auth from './auth/auth.service'
import response from './response/response.service'
import comments from './comments/comments.service'
import log from './log/log.service'
import files from './files/files.service'
import filesUser from './files_user/files_user.service'
import drawpm from './drawpm/drawpm.service'
import statsTarget from './stats-target/stats-target.service'
import tags from './tags/tags.service'
import tagsTerms from './tags-terms/tags-terms.service'
import targets from './targets/targets.service'
import tagsKnowledge from './tags-knowledge/tags-knowledge.service'
import conf from './conf/conf.service'
import tagsExt from './tags-ext/tags-ext.service'
import confUser from './conf-user/conf-user.service'
import slides from './slides/slides.service'
import materials from './materials/materials.service'
import slidesTips from './slides-tips/slides-tips.service'
import questions from './questions/questions.service'
import outlines from './outlines/outlines.service'
import promotes from './promotes/promotes.service'
import curriculum from './curriculum/curriculum.service'
import curriculumSubject from './curriculum-subject/curriculum-subject.service'
import userCert from './user-cert/user-cert.service'
import schoolPlan from './school-plan/school-plan.service'
import cache from './cache/cache.service'
import notice from './notice/notice.service'
import taskOutline from './task-outline/task-outline.service'
import unit from './unit/unit.service'
import collect from './collect/collect.service'
import confSchool from './conf-school/conf-school.service'
import classes from './classes/classes.service'
import reviews from './reviews/reviews.service'
import tool from './tool/tool.service'
import session from './session/session.service'
import members from './members/members.service'
import membersGroup from './members_group/members_group.service'
import users from './users/users.service'
import rooms from './rooms/rooms.service'
import feedback from './feedback/feedback.service'
import userToken from './user-token/user-token.service'
import noticeTpl from './notice-tpl/notice-tpl.service'
import templates from './templates/templates.service'
import zoomMeet from './zoom-meet/zoom-meet.service'
import order from './order/order.service'
import schoolUser from './school-user/school-user.service'
import collab from './collab/collab.service'
import schoolLog from './school-log/school-log.service'
import unitTpl from './unit-tpl/unit-tpl.service'
import historyTool from './history-tool/history-tool.service'
import reflection from './reflection/reflection.service'
import toolData from './tool-data/tool-data.service'
import subjects from './subjects/subjects.service'
import curric from './curric/curric.service'
import skills from './skills/skills.service'
import students from './students/students.service'
import sessionDraw from './session-draw/session-draw.service'
import unitTplUser from './unit-tpl-user/unit-tpl-user.service'
import schoolYear from './school-year/school-year.service'
import schoolTerm from './school-term/school-term.service'
import paypal from './paypal/paypal.service'
import braintree from './braintree/braintree.service'
import cart from './cart/cart.service'
import section from './section/section.service'
import sectionTracking from './section-tracking/section-tracking.service'
import serviceAuth from './service-auth/service-auth.service'
import serviceConf from './service-conf/service-conf.service'
import servicePack from './service-pack/service-pack.service'
import servicePackUser from './service-pack-user/service-pack-user.service'
import serviceBooking from './service-booking/service-booking.service'
import serviceRating from './service-rating/service-rating.service'
import userNoticeSetting from './user-notice-setting/user-notice-setting.service'
import serviceFans from './service-fans/service-fans.service'
import poster from './poster/poster.service'
import teachingAccident from './teaching-accident/teaching-accident.service'
import suspendClass from './suspend-class/suspend-class.service'
import template from './template/template.service'
import shareInfo from './share-info/share-info.service'
import sessionTakeaway from './session-takeaway/session-takeaway.service'
import shortLink from './short-link/short-link.service'
import sessionTakeawaySnapshot from './session-takeaway-snapshot/session-takeaway-snapshot.service'
import sessionSnapshot from './session-snapshot/session-snapshot.service'
import commissionSetting from './commission-setting/commission-setting.service'
import pointSetting from './point-setting/point-setting.service'
import pointLog from './point-log/point-log.service'
import manager from './manager/manager.service'
import agreement from './agreement/agreement.service'
import salesFollowUp from './sales-follow-up/sales-follow-up.service'
import servicePackUserLogs from './service-pack-user-logs/service-pack-user-logs.service'
import servicePackUserData from './service-pack-user-data/service-pack-user-data.service'
import criteria from './criteria/criteria.service'
import servicePackApply from './service-pack-apply/service-pack-apply.service'
import servicePackSchoolPrice from './service-pack-school-price/service-pack-school-price.service'
import servicePackTicket from './service-pack-ticket/service-pack-ticket.service'
import prompts from './prompts/prompts.service'
import library from './library/library.service'
import serviceAuthMessage from './service-auth-message/service-auth-message.service'
import interactiveVideoes from './interactive-videoes/interactive-videoes.service'
import campusLocation from './campus-location/campus-location.service'
import taskCategory from './task-category/task-category.service'
import message from './message/message.service'
import criteriaWeight from './criteria-weight/criteria-weight.service'
import suspendLogs from './suspend-logs/suspend-logs.service'
import announcement from './announcement/announcement.service'
import paypalWebhook from './paypal-webhook/paypal-webhook.service'
import classApply from './class-apply/class-apply.service'
import classQuestionLogs from './class-question-logs/class-question-logs.service'
import schoolTermPlan from './school-term-plan/school-term-plan.service'
import journals from './journals/journals.service'
import journalLikes from './journal-likes/journal-likes.service'
import journalComments from './journal-comments/journal-comments.service'
import sectionComments from './section-comments/section-comments.service'
import commissionLog from './commission-log/commission-log.service'
import translate from './translate'
import incomeSetting from './income-setting/income-setting.service'
import ambassadorAuth from './ambassador-auth/ambassador-auth.service'
import bonusSetting from './bonus-setting/bonus-setting.service'
import giftCard from './gift-card/gift-card.service'
import giftCardLog from './gift-card-log/gift-card-log.service'
import payoutAccounts from './payout-accounts/payout-accounts.service'
import payoutTransactions from './payout-transactions/payout-transactions.service'
import airwallex from './airwallex/airwallex.service'
import stripe from './stripe/stripe.service'
import stripeWebhook from './stripe-webhook/stripe-webhook.service'
import paymentMethods from './payment-methods/payment-methods.service'
import incomeLog from './income-log/income-log.service'
import walletBalance from './wallet-balance/wallet-balance.service'
// Don't remove this comment. It's needed to format import lines nicely.

export default function (app: Application): void {
  app.configure(test)
  app.configure(auth)
  app.configure(response)
  app.configure(comments)
  app.configure(log)
  app.configure(files)
  app.configure(filesUser)
  app.configure(drawpm)
  app.configure(statsTarget)
  app.configure(tags)
  app.configure(tagsTerms)
  app.configure(targets)
  app.configure(tagsKnowledge)
  app.configure(conf)
  app.configure(tagsExt)
  app.configure(confUser)
  app.configure(slides)
  app.configure(materials)
  app.configure(slidesTips)
  app.configure(questions)
  app.configure(outlines)
  app.configure(promotes)
  app.configure(curriculum)
  app.configure(curriculumSubject)
  app.configure(userCert)
  app.configure(schoolPlan)
  app.configure(cache)
  app.configure(notice)
  app.configure(taskOutline)
  app.configure(unit)
  app.configure(collect)
  app.configure(confSchool)
  app.configure(classes)
  app.configure(reviews)
  app.configure(tool)
  app.configure(session)
  app.configure(members)
  app.configure(membersGroup)
  app.configure(users)
  app.configure(rooms)
  app.configure(feedback)
  app.configure(userToken)
  app.configure(noticeTpl)
  app.configure(templates)
  app.configure(zoomMeet)
  app.configure(order)
  app.configure(schoolUser)
  app.configure(collab)
  app.configure(schoolLog)
  app.configure(unitTpl)
  app.configure(historyTool)
  app.configure(reflection)
  app.configure(toolData)
  app.configure(subjects)
  app.configure(curric)
  app.configure(skills)
  app.configure(students)
  app.configure(sessionDraw)
  app.configure(unitTplUser)
  app.configure(schoolYear)
  app.configure(schoolTerm)
  app.configure(paypal)
  app.configure(braintree)
  app.configure(cart)
  app.configure(section)
  app.configure(sectionTracking)
  app.configure(serviceAuth)
  app.configure(serviceConf)
  app.configure(servicePack)
  app.configure(servicePackUser)
  app.configure(serviceBooking)
  app.configure(serviceRating)
  app.configure(userNoticeSetting)
  app.configure(serviceFans)
  app.configure(poster)
  app.configure(teachingAccident)
  app.configure(suspendClass)
  app.configure(template)
  app.configure(shareInfo)
  app.configure(sessionTakeaway)
  app.configure(shortLink)
  app.configure(sessionTakeawaySnapshot)
  app.configure(sessionSnapshot)
  app.configure(commissionSetting)
  app.configure(pointSetting)
  app.configure(pointLog)
  app.configure(manager)
  app.configure(agreement)
  app.configure(salesFollowUp)
  app.configure(servicePackUserLogs)
  app.configure(servicePackUserData)
  app.configure(criteria)
  app.configure(servicePackApply)
  app.configure(servicePackSchoolPrice)
  app.configure(servicePackTicket)
  app.configure(prompts)
  app.configure(library)
  app.configure(serviceAuthMessage)
  app.configure(interactiveVideoes)
  app.configure(campusLocation)
  app.configure(taskCategory)
  app.configure(message)
  app.configure(criteriaWeight)
  app.configure(suspendLogs)
  app.configure(announcement)
  app.configure(paypalWebhook)
  app.configure(classApply)
  app.configure(classQuestionLogs)
  app.configure(schoolTermPlan)
  app.configure(journals)
  app.configure(journalLikes)
  app.configure(journalComments)
  app.configure(sectionComments)
  app.configure(commissionLog)
  app.configure(translate)
  app.configure(incomeSetting)
  app.configure(ambassadorAuth)
  app.configure(bonusSetting)
  app.configure(giftCard)
  app.configure(giftCardLog)
  app.configure(payoutAccounts)
  app.configure(payoutTransactions)
  app.configure(airwallex)
  app.configure(stripe)
  app.configure(stripeWebhook)
  app.configure(paymentMethods)
  app.configure(incomeLog)
  app.configure(walletBalance)
}
