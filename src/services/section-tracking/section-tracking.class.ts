import {HookContext} from '@feathersjs/feathers'
import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {commitTransactionSession, rollbackTransactionSession, startTransactionSession} from '../../hooks/dbTransactions'

export class SectionTracking extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async generateServicerIncome(d: HookContext) {
    const results = []
    if (d.result && Array.isArray(d.result) && d.result.length > 0) {
      d.result.forEach((item) => {
        if (item.incomeStatus === 'actual_pending') {
          results.push(item)
        }
      })
    } else if (d.result && d.result.incomeStatus === 'actual_pending') {
      results.push(d.result)
    }

    if (!results.length) return d

    const session = await startTransactionSession(this.app)
    const transactionParams = {mongoose: {session}, sideEffectsToExecute: []}
    try {
      for (const result of results) {
        await this.app.service('income-log').createIncomeLog(
          {
            uid: result.servicer,
            isSchool: false,
            tab: 'earn',
            category: 'associated_task',
            status: 1,
            businessId: result.sectionId,
            event_details: {
              id: result.sectionId,
              name: result.sectionSnapshot?.name,
              cover: result.sectionSnapshot?.taskDetails?.cover,
              session_status: result.status,
            },
            amount: result.creditedPoints * 100,
          },
          transactionParams
        )
        await this.app.service('section-tracking').patch(result._id, {incomeStatus: 'actual_processed'}, {session})
      }
      await commitTransactionSession(session, this.app, transactionParams)
    } catch (error) {
      await rollbackTransactionSession(session)
    }
    return d
  }
}
