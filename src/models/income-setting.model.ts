// income-setting-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'incomeSetting'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      tab: {type: String, enum: ['earn', 'claim'], default: 'earn'}, //类目1 earn:获取 claim:使用
      category: {
        type: String,
        required: true,
        enum: [
          'prompt', // prompt
          'self_study', // 非视频类课件
          'self_study_video', // 视频课件

          'library_content',
          'new_prompt_content',
          'self_study_content',
          'premium_contents_unit_module',
          'premium_contents_task_sessions',
          'premium_content_audit',
          'teaching_session_own',
          'teaching_session_classcipe_content',
          'associated_task',
          'certificate',
        ],
      },
      mode: {type: String, required: true, default: 'percentage', enum: ['fixed', 'percentage']}, //奖励模式 固定数值/按比例
      value: {type: Number, required: true}, //固定数值;按比例
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
