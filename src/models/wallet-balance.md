```ts
export interface WalletBalance {
  uid: string // User or School ID
  isSchool: boolean // True if uid refers to school
  balanceType: 'giftCard' | 'commission' | 'earnings' // Type of balance
  availableBalance: number // Available balance in cents
  reservedBalance: number // Currently reserved amount in cents
  totalBalance: number // available + reserved (computed field)
  lastTransactionId?: string // Last transaction that affected this balance
  lastTransactionDate?: Date // When last transaction occurred
  version: number // For optimistic locking
}
```
