// wallet-balance-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'walletBalance'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      uid: {type: String, required: true}, // User or School ID
      isSchool: {type: Boolean, required: true, default: false}, // True if uid refers to school
      balanceType: {
        type: String,
        required: true,
        enum: ['giftCard', 'commission', 'earnings'], // Type of balance
      },
      availableBalance: {type: Number, required: true, default: 0}, // Available balance in cents
      reservedBalance: {type: Number, required: true, default: 0}, // Currently reserved amount in cents
      lastTransactionId: {type: String}, // Last transaction that affected this balance
      lastTransactionDate: {type: Date}, // When last transaction occurred
      version: {type: Number, required: true, default: 0}, // For optimistic locking
    },
    {
      timestamps: true,
    }
  )

  // Add compound index for uid, isSchool, and balanceType
  schema.index({uid: 1, isSchool: 1, balanceType: 1}, {unique: true})

  // Virtual field for totalBalance (available + reserved)
  schema.virtual('totalBalance').get(function () {
    return this.availableBalance + this.reservedBalance
  })

  // Ensure virtual fields are serialized
  schema.set('toJSON', {virtuals: true})
  schema.set('toObject', {virtuals: true})

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
