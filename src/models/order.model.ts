// order-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'order'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      buyer: {type: String, required: true, index: true}, // buyer user._id/school-plan._id
      schoolAdmin: {type: String}, // 机构下单 下单的学校管理员
      // seller: {type: String}, //seller已废弃,改用sellers
      sellers: {type: Array},
      name: {type: String},
      no: {type: String},
      // link已废弃,改用links
      // link: {
      //   id: {type: String}, // link id, Ex: task.id, unit.id, workshop.id
      //   name: {type: String},
      //   mode: {type: String}, // unit.mode
      //   type: {type: String}, // 2:unit plan; 4:task; 6:evaluation, old.content_type
      //   newId: {type: String},
      //   hash: {type: String},
      // },
      links: [
        {
          id: {type: String}, // link id, Ex: task.id, unit.id, workshop.id
          name: {type: String},
          mode: {type: String}, // unit.mode
          type: {type: String}, // 2:unit plan; 4:task; 6:evaluation, old.content_type
          newId: {type: String},
          hash: {type: String},
          cover: {type: String},
          price: {type: Number},
          point: {type: Number},
          style: {type: String}, //unit session service service_premium service_substitute premium_cloud prompt section_top_up gift_card
          goods: {type: Object}, //下单时商品快照
          discountConfig: {type: Object}, // discount config applied at the time of purchase
          sessionId: {type: Object}, //捆绑服务包的公开课_id,prompt购买插入的课程
          count: {type: Object}, //服务包次数 不包含赠送次数
          sectionCount: {type: Number}, // number of sections purchased for associated tasks, defualt not set(purchase all sections)
          // gift: {type: Boolean}, // 弃用 更换为promotion
          promotion: {type: Boolean}, // 是否赠品/推广 promotion
          giftCount: {type: Number, default: 0}, // 赠送次数
          removed: {type: Boolean}, //支付前 被下架或删除; 或已取消已退款
          inviter: {type: String, trim: true}, //分享人
          schoolInviter: {type: String, trim: true}, //分享人为学校 school-plan._id
          inviteSource: {type: String, trim: true, enum: ['new_prompt', 'sales_follow_up']}, //分享来源,new-prompt;sales-follow-up: 销售跟进
          inviteSourceId: {type: String, trim: true}, //目前只有一个场景,从new prompt下分享的workshop的_id
          archived: {type: Boolean, default: false}, //archived and deleted
          persons: {type: Number, default: 1}, // 主题服务包 学校购买 1v1服务包份数
          packUserTasks: {type: Array}, // Lecture包复购的课件id数组, 预定取消/复购/补买调用
          oldPackUser: {type: String}, // 补买用,主题服务包Lecture加到原来的packUser中
          bookingId: {type: String}, // 认证精品课快照购买支付成功后 自动排课用
          // premiumCloudUnused: {type: Boolean, default: false}, // 认证精品课快照未使用
          session: {type: String}, // premium_cloud认证精品课快照 绑定的session._id
          isOnCampus: {type: Boolean, default: false}, // 线上false, 线下true
          country: {type: String, trim: true},
          city: {type: String, trim: true},
          used: {type: Boolean, default: false}, // prompt,premium_cloud使用情况
          refundPrice: {type: Number, default: 0}, // 退款金额
          refundPoint: {type: Number, default: 0}, // 退款积分
          mentorPack: {type: String}, // For Associate Task Parent Mentoring service-pack._id
          giftCardData: {type: Object}, //to store gift card information, will be used to create gift card during order completion (after successfull payment)
        },
      ],
      /**
       * 订单状态 status 除400外的4xx弃用 | All 4xx codes except 400 are deprecated
       * 100.待支付；Pending
       * 110.支付成功,正在处理中 Payment Complete, Processing Order Completion
       * 200.支付成功；Payment successful
       * 300.支付失败；Payment failed
       * 400.支付超时 Payment has timed out 除400外的4xx弃用 | (Only valid 4xx code; others are deprecated)
       * 401.未支付 公开课被讲师取消 canceled by the facilitator
       * 402.未支付 公开课因未成团被系统取消 Minimal registration number not met
       * 403.未支付 课件/自学习被下架 Product removed
       * 404.未支付 商品已更新 系统取消 Unpaid — Product updated, order canceled by the system
       * 500.已支付 公开课/服务包被购买者取消 canceled by the purchaser
       * 501.已支付 公开课被讲师取消 canceled by the facilitator
       * 502.已支付 公开课因未成团被系统取消 Minimal registration number not met
       * 503.已支付 支付前被下架/删除,支付后立即退款 | Paid — Product was taken down or deleted before payment; refunded immediately after payment
       * 504.订单因系统错误处理失败，已立即退款。Order processing failed due to system error. Refunded immediately.
       */
      status: {type: Number, default: 100},
      settled: {type: Boolean, default: false}, //braintree settled
      /**
       * 订单类型
       * unit
       * session_public
       * session_self_study
       * session_service_pack 捆绑服务包
       * service_pack 服务包
       * service_premium 主题服务包
       * service_substitute 代课服务包
       * premium_cloud 认证精品课快照
       * prompt
       * section_top_up
       */
      type: {
        type: String,
        enum: [
          'unit',
          'session_public',
          'session_self_study',
          'session_service_pack',
          'service_pack',
          'service_premium',
          'service_substitute',
          'premium_cloud',
          'prompt',
          'gift_card',
          'section_top_up',
          'remaining_sections',
        ],
      },
      price: {type: Number}, // Unit cent 支付金额(现金+gift card) | Payment amount in cents (cash + gift card).
      priceBreakdown: {
        cash: {type: Number, default: 0}, // Unit cent 现金支付金额 | Cash payment amount in cents.
        giftCard: {type: Number, default: 0}, // Unit cent gift card 支付金额 | Gift card payment amount in cents.
      }, // Unit cent 支付金额明细 | Payment amount breakdown.
      point: {type: Number}, // 支付积分
      // subtotal: {type: Number}, // Unit cent 商品总金额 后续增加 | Total product amount to be added later.
      // coupon: { type: Number }, // Unit cent 优惠金额 后续增加

      payMethod: {type: Array}, // 支付方式 paypal, windcave, giftCard, braintree, stripe
      paid: {type: Number, default: 0}, // 支付状态 0未支付 1已支付 2已退款 | 0 - Unpaid, 1 - Paid, 2 - Refunded
      paypalId: {type: String}, // paypal支付号
      braintreeId: {type: String}, // Braintree支付号
      stripeId: {type: String}, // Stripe payment intent ID
      /**
       * 支付信息 paymentInfo
       * {
       *  paymentInstrumentType string 支付方式
       *  cardType string 卡机构
       *  last4 string 卡号后四位
       * }
       */
      // paymentInfo: {type: Object},
      expiration: {type: Date}, // 支付超时时间
      // 退款详情
      refund: [
        {
          method: {type: String}, //paypal, windcave, giftCard, braintree
          status: {type: Number}, //状态同order status
          amount: {type: Number}, // Unit cent 退款金额,
          executed: {type: Boolean, default: true}, //退款已执行
          createdAt: {type: Date},
          executedAt: {type: Date}, //退款执行时间
        },
      ],
      paidAt: {type: Date}, // Updated when cash payment is made

      // whenever respective payment is made, add a new entry
      paymentRecords: [
        {
          method: {type: String, enum: ['paypal', 'stripe', 'giftCard']},
          status: {type: String, enum: ['pending', 'paid', 'failed', 'refunded']},
          amount: {type: Number}, // Unit cent
          transactionId: {type: String}, // e.g. Stripe charge ID (if method = stripe)
          paidAt: {type: Date},
        },
      ],

      reminder: {type: Number, default: 0}, // 未支付提醒,0: 待提醒，1: 超时前15min已提醒
      inviter: {type: String, trim: true}, //分享人 users.inviteCode
      schoolInviter: {type: String, trim: true}, //分享人为学校 school-plan._id
      inviteSource: {type: String, trim: true, enum: ['new_prompt', 'sales_follow_up']}, //分享来源,new-prompt;sales-follow-up: 销售跟进
      inviteSourceId: {type: String, trim: true}, //目前只有一个场景,从new prompt下分享的workshop的_id
      isPoint: {type: Boolean, default: false}, //积分购买
      isSeparated: {type: Boolean, default: false}, //积分/佣金是否已分账
      isTicket: {type: Boolean, default: false}, // 主题服务包 需生成代金券
      isSchool: {type: Boolean, default: false}, // buyer为学校
      sharedSchool: {type: String}, // school-plan._id 从学校分享购买的,分享的学校id
      servicePremium: {type: String}, // service-pack._id 主题服务包id
      servicePremiumSnapshot: {type: Object}, //主题服务包快照
      persons: {type: Number, default: 1}, // buyer为学校,1v1服务包份数
      servicePackApply: {type: String}, // 主题服务包申请id
      /**
       *  待处理退款信息 | Pending refund information
       * */
      refundRequired: {
        type: {
          amount: {type: Number}, // 退款金额 | Refund amount in cents
          invalidLinks: {type: Array}, // 无效链接列表 | Invalid links array
          refundLinkName: {type: Array}, // 退款链接名称列表 | Refund link name array
          refundLinkCover: {type: Array}, // 退款链接封面列表 | Refund link cover array
          createdAt: {type: Date, default: Date.now}, // 创建时间 | Created timestamp
          escalated: {type: Boolean, default: false}, // 是否已 escalated | Escalated flag
        },
        default: undefined,
        _id: false,
      },
      /**
       *  订单完成重试信息 | Order completion retry information
       * */
      retryInfo: {
        type: {
          count: {type: Number, default: 0}, // 重试次数
          reason: {type: String}, // 重试原因
          updatedAt: {type: Date, default: Date.now}, // 创建时间
        },
        default: undefined,
        _id: false,
      },
      processingLockExpiresAt: {type: Date}, // 订单处理锁过期时间 | Order processing lock expiration time
      incomeStatus: {type: String, enum: ['expected_pending', 'expected_processed', 'actual_pending', 'actual_processed']}, // if '**_pending', income will be processed by cron
    },
    {
      timestamps: true,
    }
  )

  schema.index({'refundRequired.createdAt': 1}, {partialFilterExpression: {'refundRequired.escalated': false}}) // For refund retry cron job
  schema.index({incomeStatus: 1}, {partialFilterExpression: {incomeStatus: {$in: ['expected_pending', 'actual_pending']}}}) // For income processing cron job
  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
